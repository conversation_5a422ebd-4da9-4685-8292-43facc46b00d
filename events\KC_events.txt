namespace = KC_Event

### MASTER FIRE ###
event = {
	id = KC_Event.1
	hide_window = yes
	is_triggered_only = yes

	immediate = {
		set_global_flag = KC_Setup_Fired

		#Energy Agronomics
		every_playable_country = {
			limit = { has_civic = KC_energy_agronomics }
			country_event = { id = KC_Event.2 }
		}

		#Galactic Food Industries
		every_playable_country = {
			limit = { has_civic = KC_galactic_food_industries }
			country_event = { id = KC_Event.3 }
		}
	}
}

### NORMAL EMPIRES ###

# Energy Agronomics

# Game Start
country_event = {
	id = KC_Event.2
	hide_window = yes
	is_triggered_only = yes

	immediate = {
		give_technology = { tech = tech_food_processing_1 message = no }
		random_owned_planet = {
			limit = { is_capital = yes }

			if = {
				limit = {
					owner = {
						has_origin = origin_shattered_ring
					}
				}
				add_zone = {
					district = district_farming_uncapped
					zone = KC_zone_food_energy
					replace = yes
				}
			}

			else = {
				add_zone = {
					district = district_farming
					zone = KC_zone_food_energy
					replace = yes
				}
			}
		}
	}
}

### MEGACORP EMPIRES ###

# Galactic Food Industries

# Game Start
country_event = {
	id = KC_Event.3
	hide_window = yes
	is_triggered_only = yes

	immediate = {
		give_technology = { tech = tech_food_processing_1 message = no }
		random_owned_planet = {
			limit = { is_capital = yes }

			if = {
				limit = {
					owner = {
						has_origin = origin_shattered_ring
					}
				}
				add_zone = {
					district = district_farming_uncapped
					zone = KC_zone_food_trade
					replace = yes
				}
			}

			else = {
				add_zone = {
					district = district_farming
					zone = KC_zone_food_trade
					replace = yes
				}
			}
		}
	}
}

### Firewall Protocols ###

## KC_Event.4 - on-action path: apply immediately, roll bonus in after
country_event = {
    id = KC_Event.4
    hide_window = yes
    is_triggered_only = yes

    trigger = { has_valid_civic = KC_firewall_protocols }

    immediate = {
        from = { KC_Apply_Firewall_Protocols = yes }
    }

    after = {
        from = {
            if = { limit = { kc_valid_for_additional_trait = yes } KC_Generate_Rando_Gov_trait = yes }
        }
    }
}

# KC_Event.5 - daily fixer: handle OWNED and POOL leaders
country_event = {
    id = KC_Event.5
    hide_window = yes

    trigger = {
        has_valid_civic = KC_firewall_protocols

        OR = {
            # owned leaders who need work
            any_owned_leader = {
                leader_class = official
                OR = {
                    NOR = {
                        has_trait = leader_trait_righteous
                        has_trait = leader_trait_righteous_2
                    }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }

            # pool leaders who need work
            any_pool_leader = {
                leader_class = official
                OR = {
                    NOR = {
                        has_trait = leader_trait_righteous
                        has_trait = leader_trait_righteous_2
                    }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }
        }
    }

    mean_time_to_happen = { days = 1 }

    immediate = {
        # fix owned leaders
        every_owned_leader = {
            limit = {
                leader_class = official
                OR = {
                    NOR = {
                        has_trait = leader_trait_righteous
                        has_trait = leader_trait_righteous_2
                    }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }
            KC_Apply_Firewall_Protocols = yes
            if = { limit = { kc_valid_for_additional_trait = yes } KC_Generate_Rando_Gov_trait = yes }
        }

        # fix pool leaders - guarantees pool cards are correct
        every_pool_leader = {
            limit = {
                leader_class = official
                OR = {
                    NOR = {
                        has_trait = leader_trait_righteous
                        has_trait = leader_trait_righteous_2
                    }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }
            KC_Apply_Firewall_Protocols = yes
            if = { limit = { kc_valid_for_additional_trait = yes } KC_Generate_Rando_Gov_trait = yes }
        }
    }
}

### SHARED EVENTS ###
# These events are shared among multiple civics. This is most likely cross-empire type civics.

# events/military_doctrine_picker.txt
country_event = {
    id = KC_doctrine.1
    title = KC_doctrine.1.title
    desc = KC_doctrine.1.desc
    picture = GFX_evt_fleet
    show_sound = event_situation_started
    is_triggered_only = yes
    hide_window = no

    # optional safety
    trigger = { has_civic = KC_military_doctrine }

    option = {                                   # SWARM TACTICS - all empires
        name = KC_doctrine.1.a
        custom_tooltip = KC_doctrine.1.a.tt
        trigger = { always = yes }
        ai_chance = { factor = 1 }
        set_country_flag = kc_doctrine_swarm
        add_country_modifier = { modifier = kc_swarm_doctrine_modifier days = -1 }
        # grant Aggressive to admirals in hand now and in the recruit pool
        every_owned_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_aggressive } }
            add_trait = { trait = leader_trait_aggressive show_message = no }
        }
        every_pool_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_aggressive } }
            add_trait = { trait = leader_trait_aggressive show_message = no }
        }
        # your corvette cost/time modifiers live in kc_swarm_doctrine_modifier
    }

    option = {                                   # UNYIELDING - all empires
        name = KC_doctrine.1.b
        custom_tooltip = KC_doctrine.1.b.tt
        trigger = { always = yes }
        ai_chance = { factor = 1 }
        set_country_flag = kc_doctrine_unyielding
        add_country_modifier = { modifier = kc_unyielding_doctrine_modifier days = -1 } # +hull -disengage
        add_technology = "tech_destroyers"
        every_owned_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_unyielding } }
            add_trait = { trait = leader_trait_unyielding show_message = no }
        }
        every_pool_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_unyielding } }
            add_trait = { trait = leader_trait_unyielding show_message = no }
        }
    }

    option = {                                   # NANITES - machines only
        name = KC_doctrine.1.c_machines
        custom_tooltip = KC_doctrine.1.c_machines.tt
        trigger = { is_machine_empire = yes }
        ai_chance = { factor = 1 }
        set_country_flag = kc_doctrine_nanites
        add_country_modifier = { modifier = kc_nanite_doctrine_modifier days = -1 } # building speed and upkeep
        every_owned_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_nanite_repair } }
            add_trait = { trait = leader_trait_nanite_repair show_message = no }
        }
        every_pool_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_nanite_repair } }
            add_trait = { trait = leader_trait_nanite_repair show_message = no }
        }
    }

    option = {                                   # BIOMASS ENGINEERS - hives only
        name = KC_doctrine.1.c_hives
        custom_tooltip = KC_doctrine.1.c_hives.tt
        trigger = { is_hive_mind = yes }
        ai_chance = { factor = 1 }
        set_country_flag = kc_doctrine_biomass
        add_country_modifier = { modifier = kc_biomass_doctrine_modifier days = -1 } # building speed and food upkeep tweak
        every_owned_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_resilient } }
            add_trait = { trait = leader_trait_resilient show_message = no } # vanilla stand-in for regen theme
        }
        every_pool_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_resilient } }
            add_trait = { trait = leader_trait_resilient show_message = no }
        }
    }

    option = {                                   # ENGINEERS - regular or corporate only
        name = KC_doctrine.1.c_regulars
        custom_tooltip = KC_doctrine.1.c_regulars.tt
        trigger = { NOT = { OR = { is_machine_empire = yes is_hive_mind = yes } } }
        ai_chance = { factor = 1 }
        set_country_flag = kc_doctrine_engineers
        add_country_modifier = { modifier = kc_engineer_doctrine_modifier days = -1 } # ship repair speed, starbase econ
        every_owned_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_engineer } }
            add_trait = { trait = leader_trait_engineer show_message = no }
        }
        every_pool_leader = {
            limit = { leader_class = admiral NOT = { has_trait = leader_trait_engineer } }
            add_trait = { trait = leader_trait_engineer show_message = no }
        }
    }
}